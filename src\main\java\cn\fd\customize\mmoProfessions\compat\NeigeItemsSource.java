package cn.fd.customize.mmoProfessions.compat;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import pers.neige.neigeitems.item.ItemInfo;
import pers.neige.neigeitems.manager.ItemManager;

/**
 * NeigeItems物品源实现
 * 处理NeigeItems插件的物品
 */
public class NeigeItemsSource extends AbstractItemSource {

    private final String itemId;

    public NeigeItemsSource(ItemSourceFactory factory, String value, String amount) {
        super(factory, value, amount);
        this.itemId = value;
    }

    @Override
    public boolean matchesIngredient(ItemStack item) {
        if (item == null || item.getType().isAir()) {
            return false;
        }

        ItemInfo info = ItemManager.INSTANCE.isNiItem(item);

        return ItemManager.INSTANCE.isNiItem(item) != null
                && info.getId().equals(itemId)
                && item.getAmount() == getRequiredAmount();
    }

    @Override
    public ItemStack generateOutput(Player player) {
        return applyAmount(ItemManager.INSTANCE.getItemStack(itemId, player));
    }

}
