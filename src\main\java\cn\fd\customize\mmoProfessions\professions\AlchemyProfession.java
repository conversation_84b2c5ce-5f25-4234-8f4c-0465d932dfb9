package cn.fd.customize.mmoProfessions.professions;

import org.bukkit.entity.Player;

/**
 * 炼金职业
 */
public class AlchemyProfession extends AbstractProfession {

    public AlchemyProfession() {
        super("alchemy", "炼金", "通过炼制药剂和材料获得经验");
    }

    @Override
    public void onExpGain(Player player, int amount) {
        super.onExpGain(player, amount);
        // 可以添加炼金特有的经验获得效果
    }

    @Override
    public void onLevelUp(Player player, int newLevel) {
        super.onLevelUp(player, newLevel);
        // 特殊升级消息现在通过MessageManager在messages.yml中配置
    }

    /**
     * 检查玩家是否可以炼制指定等级的配方
     */
    public boolean canCraft(Player player, int requiredLevel) {
        return hasLevel(player, requiredLevel);
    }

    /**
     * 获取炼金成功率加成（基于等级）
     */
    public double getSuccessRateBonus(Player player) {
        int level = getLevel(player);
        return Math.min(0.3, level * 0.005); // 每级增加0.5%成功率，最高30%
    }

    /**
     * 获取稳定剂效果加成（基于等级）
     */
    public double getStabilizerBonus(Player player) {
        int level = getLevel(player);
        if (level < 40)
            return 1.0;
        return 1.0 + ((level - 40) * 0.02); // 40级后每级增加2%稳定剂效果
    }

    /**
     * 获取额外产物几率（基于等级）
     */
    public double getExtraOutputChance(Player player) {
        int level = getLevel(player);
        if (level < 30)
            return 0.0;
        return Math.min(0.25, (level - 30) * 0.01); // 30级后每级增加1%，最高25%
    }

    /**
     * 获取炼金时间减少比例（基于等级）
     */
    public double getTimeReduction(Player player) {
        int level = getLevel(player);
        return Math.min(0.5, level * 0.008); // 每级减少0.8%时间，最高减少50%
    }

}
