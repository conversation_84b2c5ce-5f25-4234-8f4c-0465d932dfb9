package cn.fd.customize.mmoProfessions.core;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.alchemy.AlchemyTask;
import cn.fd.customize.mmoProfessions.serialize.ItemStackTypeAdapter;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitTask;

import java.io.*;
import java.util.*;

/**
 * 玩家数据存储管理器
 * 专门处理JSON格式的玩家职业数据保存和加载
 */
public class PlayerDataStorage {

    private final Gson gson;
    private final File dataFolder;
    private static BukkitTask aotoSaveTask;

    public PlayerDataStorage() {
        // 初始化JSON工具，注册序列化器
        this.gson = new GsonBuilder()
                .setPrettyPrinting()
                .excludeFieldsWithoutExposeAnnotation()
                .registerTypeAdapter(ItemStack.class, new ItemStackTypeAdapter())
                .create();

        // 初始化数据文件夹
        this.dataFolder = new File(MMOProfessions.getInstance().getDataFolder(), "data");
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }
    }

    /**
     * 启动自动保存任务 - 每30秒保存在线玩家的所有数据
     */
    public void startAutoSaveTask() {
        aotoSaveTask = Bukkit.getScheduler().runTaskTimerAsynchronously(MMOProfessions.getInstance(), () -> {
            saveAllPlayerData();
        }, 20L * 30, 20L * 30); // 每30秒保存一次
    }

    public void cancelAutoSaveTask() {
        if (aotoSaveTask != null) {
            aotoSaveTask.cancel();
        }
    }

    /**
     * 保存所有在线玩家数据
     */
    public void saveAllPlayerData() {
        for (Player onlinePlayer : MMOProfessions.getInstance().getServer().getOnlinePlayers()) {
            savePlayerData(onlinePlayer.getUniqueId());
        }
    }

    /**
     * 保存玩家所有数据
     */
    public void savePlayerData(UUID playerId) {
        try {
            PlayerResolvedData playerData = new PlayerResolvedData();

            // 保存职业数据
            playerData.professions = new HashMap<>();
            ProfessionData professionData = MMOProfessions.getProfessionManager().getPlayerData(playerId);
            for (String professionId : professionData.getGrantedProfessions()) {
                SingleProfessionData professionJson = new SingleProfessionData();
                professionJson.granted = professionData.hasProfession(professionId);
                professionJson.experience = professionData.getExperience(professionId);
                playerData.professions.put(professionId, professionJson);
            }

            // 保存队列解锁信息
            playerData.unlocked_queue_count = MMOProfessions.getAlchemyManager().getTaskManager()
                    .getUnlockedQueueCount(playerId);

            // 保存炼金输出队列
            playerData.alchemy_outputs_queue = new ArrayList<>(
                    MMOProfessions.getAlchemyManager().getMainGUI().getPlayerOutputQueue(playerId));

            // 保存炼金任务
            playerData.alchemy_tasks = new HashMap<>(
                    MMOProfessions.getAlchemyManager().getTaskManager().getActiveTasks(playerId));

            // 保存到文件
            File playerFile = getPlayerDataFile(playerId);
            // 确保父目录存在
            if (!playerFile.getParentFile().exists()) {
                playerFile.getParentFile().mkdirs();
            }

            FileWriter writer = new FileWriter(playerFile);
            gson.toJson(playerData, writer);
            writer.flush(); // 确保数据被写入磁盘
            writer.close();

        } catch (Exception e) {
            MMOProfessions.getInstance().getLogger().severe("保存玩家数据失败: " + playerId + " - " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 加载玩家所有数据
     */
    public PlayerResolvedData loadPlayerData(UUID playerId) {
        PlayerResolvedData playerData = null;
        try {
            File playerFile = getPlayerDataFile(playerId);
            if (playerFile.exists()) {
                playerData = gson.fromJson(new FileReader(playerFile), PlayerResolvedData.class);
            }
        } catch (Exception e) {
            MMOProfessions.getInstance().getLogger().severe("加载玩家数据失败: " + playerId + " - " + e.getMessage());
            e.printStackTrace();
        }

        // 空纠正
        if (playerData == null) {
            playerData = new PlayerResolvedData();
        }
        if (playerData.professions == null) {
            playerData.professions = new HashMap<>();
        }

        if (playerData.unlocked_queue_count <= 0) {
            // 设置默认的基础队列数
            playerData.unlocked_queue_count = MMOProfessions.getAlchemyManager().getConfigManager().getBaseQueueCount();
        }
        if (playerData.alchemy_outputs_queue == null) {
            playerData.alchemy_outputs_queue = new ArrayList<>();
        }
        if (playerData.alchemy_tasks == null) {
            playerData.alchemy_tasks = new HashMap<>();
        }

        // 恢复炼金输出队列到GUI
        MMOProfessions.getAlchemyManager().getMainGUI().setPlayerOutputQueue(playerId,
                playerData.alchemy_outputs_queue);

        return playerData;
    }

    /**
     * JSON数据结构 - 玩家数据
     */
    public static class PlayerResolvedData {
        @Expose
        public Map<String, SingleProfessionData> professions;
        @Expose
        public int unlocked_queue_count; // 解锁的队列数量
        @Expose
        public List<ItemStack> alchemy_outputs_queue; // 炼金输出物品队列
        @Expose
        public Map<Integer, AlchemyTask> alchemy_tasks; // 炼金任务存储

        /**
         * 获取职业数据
         */
        public ProfessionData getProfessionData(UUID playerId) {
            HashMap<String, Integer> experience = new HashMap<>();
            List<String> grantedProfessions = new ArrayList<>();
            for (Map.Entry<String, SingleProfessionData> entry : professions.entrySet()) {
                if (entry.getValue().granted) {
                    grantedProfessions.add(entry.getKey());
                }
                experience.put(entry.getKey(), entry.getValue().experience);
            }
            return new ProfessionData(playerId, experience, grantedProfessions);
        }
    }

    /**
     * JSON数据结构 - 职业数据
     */
    public static class SingleProfessionData {
        @Expose
        public boolean granted;
        @Expose
        public int experience;
    }

    /**
     * 获取玩家数据文件
     */
    public File getPlayerDataFile(UUID playerId) {
        return new File(dataFolder, playerId.toString() + ".json");
    }

}
