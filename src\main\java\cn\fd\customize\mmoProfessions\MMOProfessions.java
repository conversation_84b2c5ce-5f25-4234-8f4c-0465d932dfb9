package cn.fd.customize.mmoProfessions;

import cn.fd.customize.mmoProfessions.alchemy.AlchemyManager;
import cn.fd.customize.mmoProfessions.area.AreaManager;
import cn.fd.customize.mmoProfessions.commands.AlchemyCommand;
import cn.fd.customize.mmoProfessions.commands.ProfessionCommand;
import cn.fd.customize.mmoProfessions.compat.ItemSourceRegistry;
import cn.fd.customize.mmoProfessions.compat.PlaceholderAPIExpansion;
import cn.fd.customize.mmoProfessions.compat.ProfessionCondition;
import cn.fd.customize.mmoProfessions.config.ConfigManager;
import cn.fd.customize.mmoProfessions.core.ExperienceManager;
import cn.fd.customize.mmoProfessions.core.PlayerDataStorage;
import cn.fd.customize.mmoProfessions.config.MessageManager;
import cn.fd.customize.mmoProfessions.core.ProfessionManager;
import cn.fd.customize.mmoProfessions.listeners.AlchemyGUIListener;
import cn.fd.customize.mmoProfessions.listeners.AreaBlockBreakListener;
import cn.fd.customize.mmoProfessions.listeners.AreaProtectionListener;
import cn.fd.customize.mmoProfessions.listeners.PlayerDataListener;
import cn.fd.customize.mmoProfessions.professions.AlchemyProfession;
import cn.fd.customize.mmoProfessions.professions.ForgingProfession;
import cn.fd.customize.mmoProfessions.professions.LumberingProfession;
import cn.fd.customize.mmoProfessions.professions.MiningProfession;
import net.Indyuce.mmoitems.MMOItems;
import net.Indyuce.mmoitems.api.crafting.ConditionalDisplay;

import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

public final class MMOProfessions extends JavaPlugin {

    private static MMOProfessions instance;
    private ConfigManager configManager;
    private ProfessionManager professionManager;
    private ExperienceManager experienceManager;
    private MessageManager messageManager;
    private AreaManager areaManager;
    private AlchemyManager alchemyManager;
    private PlayerDataStorage dataStorage;

    @Override
    public void onLoad() {
        instance = this;

        // 初始化管理器
        configManager = new ConfigManager();
        messageManager = new MessageManager();
        dataStorage = new PlayerDataStorage();
        professionManager = new ProfessionManager();
        experienceManager = new ExperienceManager();
        areaManager = new AreaManager();

        // 注册默认职业
        registerDefaultProfessions();

        // 注册 ProfessionCondition (MMOItems 条件)
        if (Bukkit.getPluginManager().getPlugin("MMOItems") != null) {
            try {
                // 用法：perfession{id="mining", level=10}
                MMOItems.plugin.getCrafting().registerCondition("profession", ProfessionCondition::new, new ConditionalDisplay(
                        configManager.getProfessionConditionPositiveDisplay(),
                        configManager.getProfessionConditionNegativeDisplay()));
                getLogger().info("已注册MMOItems职业条件");
            } catch (Exception e) {
                getLogger().warning("注册MMOItems职业条件失败: " + e.getMessage());
            }
        } else {
            getLogger().info("MMOItems插件未安装，跳过职业条件注册");
        }
    }

    @Override
    public void onEnable() {
        // 初始化物品源注册器
        ItemSourceRegistry.initialize();

        // 初始化区域系统
        areaManager.loadAreas();

        // 启动自动保存任务
        dataStorage.startAutoSaveTask();

        // 初始化炼金系统
        alchemyManager = new AlchemyManager();
        alchemyManager.getMainGUI().startQueueRefreshTask();

        // 注册事件监听器
        getServer().getPluginManager().registerEvents(new AreaBlockBreakListener(), this);
        getServer().getPluginManager().registerEvents(new AreaProtectionListener(), this);
        getServer().getPluginManager().registerEvents(new AlchemyGUIListener(), this);
        getServer().getPluginManager().registerEvents(new PlayerDataListener(), this);

        // 注册命令处理器
        ProfessionCommand professionCommand = new ProfessionCommand();
        getCommand("mmoprofessions").setExecutor(professionCommand);
        getCommand("mmoprofessions").setTabCompleter(professionCommand);

        // 注册炼金命令
        AlchemyCommand alchemyCommand = new AlchemyCommand();
        getCommand("alchemy").setExecutor(alchemyCommand);
        getCommand("alchemy").setTabCompleter(alchemyCommand);

        // 初始化 PlaceholderAPI
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            try {
                new PlaceholderAPIExpansion().register();
                getLogger().info("PlaceholderAPI 已挂钩");
            } catch (Exception e) {
                getLogger().warning("无法注册 PlaceholderAPI 扩展: " + e.getMessage());
            }
        } else {
            getLogger().info("PlaceholderAPI 插件未找到，跳过集成");
        }

        getLogger().info("MMOProfessions 插件已启用！");
    }

    @Override
    public void onDisable() {
        // 停止自动保存任务
        dataStorage.cancelAutoSaveTask();

        // 取消队列按钮刷新任务
        alchemyManager.getMainGUI().cancelQueueRefreshTimer();

        // 保存所有玩家数据
        dataStorage.saveAllPlayerData();

        getLogger().info("MMOProfessions 插件已禁用！");
    }

    public static MMOProfessions getInstance() {
        return instance;
    }

    public static ConfigManager getConfigManager() {
        return getInstance().configManager;
    }

    public static PlayerDataStorage getDataStorage() {
        return getInstance().dataStorage;
    }

    public static ProfessionManager getProfessionManager() {
        return getInstance().professionManager;
    }

    public static ExperienceManager getExperienceManager() {
        return getInstance().experienceManager;
    }

    public static MessageManager getMessageManager() {
        return getInstance().messageManager;
    }

    public static AreaManager getAreaManager() {
        return getInstance().areaManager;
    }

    public static AlchemyManager getAlchemyManager() {
        return getInstance().alchemyManager;
    }

    /**
     * 重新加载所有配置
     */
    public void reloadAllConfigs() {
        configManager.reloadConfig();
        messageManager.reloadMessages();
        experienceManager.reloadExperienceConfig();
        areaManager.reloadAreas();
        alchemyManager.reload();
    }

    /**
     * 注册默认职业
     */
    private void registerDefaultProfessions() {
        professionManager.registerProfession(new MiningProfession());
        professionManager.registerProfession(new LumberingProfession());
        professionManager.registerProfession(new AlchemyProfession());
        professionManager.registerProfession(new ForgingProfession());
    }

}
