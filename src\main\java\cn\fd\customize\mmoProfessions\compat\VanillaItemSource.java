
package cn.fd.customize.mmoProfessions.compat;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * 原版物品源实现
 * 处理Minecraft原版物品
 */
public class VanillaItemSource extends AbstractItemSource {

    private final Material material;
    private final ItemStack itemStack;

    public VanillaItemSource(ItemSourceFactory factory, String value, String amount) {
        super(factory, value, amount);
        this.material = parseMaterial(value);
        this.itemStack = new ItemStack(material, getRequiredAmount());
    }

    /**
     * 从配置值中解析Material
     * @param value 配置值，直接是Material名称，如: DIAMOND 或 COAL
     * @return 解析出的Material
     */
    private Material parseMaterial(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("原版物品配置值不能为空");
        }

        String materialName = value.trim().toUpperCase();
        try {
            Material material = Material.valueOf(materialName);
            if (material.isAir()) {
                MMOProfessions.getInstance().getLogger().warning(
                    String.format("原版物品类型为AIR: %s", materialName));
            }
            return material;
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("未知的原版物品类型: " + materialName, e);
        }
    }

    @Override
    public boolean matchesIngredient(ItemStack item) {
        if (item == null || item.getType().isAir()) {
            return false;
        }

        // 简单的类型匹配
        return item.isSimilar(itemStack);
    }

    @Override
    public ItemStack generateOutput(Player player) {
        return applyAmount(itemStack.clone());
    }

}