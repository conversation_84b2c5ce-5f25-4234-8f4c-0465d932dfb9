package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.inventory.ItemStack;

import java.util.*;

/**
 * 炼金配方管理器
 * 管理炼金配方的加载、匹配和计算
 * 包私有类，只能通过AlchemyManager访问
 */
class AlchemyRecipeManager {

    private final List<AlchemyRecipe> recipes;
    private final List<Stabilizer> stabilizers;
    private final AlchemyManager alchemyManager;

    AlchemyRecipeManager(AlchemyManager alchemyManager) {
        this.alchemyManager = alchemyManager;
        MMOProfessions.getInstance().getLogger().info("正在加载炼金配方...");
        this.recipes = alchemyManager.getConfigManager().loadRecipes();
        this.stabilizers = alchemyManager.getConfigManager().loadStabilizers();
    }

    /**
     * 获取稳定剂
     */
    public Stabilizer getStabilizer(ItemStack item) {
        for (Stabilizer stabilizer : stabilizers) {
            if (stabilizer.matches(item)) {
                return stabilizer;
            }
        }
        return null;
    }

    /**
     * 获取所有配方
     */
    public List<AlchemyRecipe> getRecipes() {
        return recipes;
    }

    /**
     * 匹配配方
     * 
     * @param inputItems  输入物品列表
     * @param stabilizers 稳定剂列表（暂时未使用）
     * @param player      玩家（暂时未使用）
     * @return 匹配的配方，如果没有匹配则返回null
     */
    public AlchemyRecipe matchRecipe(List<ItemStack> inputItems) {
        for (AlchemyRecipe recipe : recipes) {
            if (recipe.matches(inputItems)) {
                return recipe;
            }
        }
        return null;
    }

    /**
     * 重新加载配方和稳定剂
     */
    public void reloadRecipes() {
        MMOProfessions.getInstance().getLogger().info("正在重新加载炼金配方...");

        // 清空现有配方和稳定剂
        recipes.clear();
        stabilizers.clear();

        // 重新加载配方和稳定剂
        recipes.addAll(alchemyManager.getConfigManager().loadRecipes());
        stabilizers.addAll(alchemyManager.getConfigManager().loadStabilizers());

        MMOProfessions.getInstance().getLogger().info("炼金配方重新加载完成，共加载 " + recipes.size() + " 个配方");
    }

}
