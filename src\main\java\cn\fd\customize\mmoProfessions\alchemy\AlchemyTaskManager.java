package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 炼金任务管理器
 * 管理炼金任务的创建和队列解锁
 */
public class AlchemyTaskManager {

    private final Map<UUID, Map<Integer, AlchemyTask>> activeTasks; // 当前活跃的任务
    private final Map<UUID, Integer> playerUnlockedQueues; // 玩家ID -> 解锁的队列数量

    public AlchemyTaskManager() {
        this.activeTasks = new ConcurrentHashMap<>();
        this.playerUnlockedQueues = new ConcurrentHashMap<>();
    }

    /**
     * 创建新的炼金任务
     */
    public AlchemyTask createTask(Player player, int queueIndex, List<ItemStack> inputItems,
            ItemStack stabilizer, List<ItemStack> outputItems, long duration) {
        AlchemyTask task = new AlchemyTask(queueIndex, inputItems, stabilizer, duration);

        // 生成输出物品
        task.setOutputItems(outputItems);

        // 添加到活跃任务管理器
        activeTasks.computeIfAbsent(player.getUniqueId(), k -> new HashMap<>()).put(queueIndex, task); // 立即保存
        // 立即保存数据
        MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());

        MMOProfessions.getInstance().getLogger().info(String.format("创建炼金任务: %d (玩家: %s, 队列: %d, 持续时间: %ds)",
                task.getTaskId(), player.getName(), queueIndex, duration / 1000));

        return task;
    }

    /**
     * 获取玩家的活跃任务
     */
    public Map<Integer, AlchemyTask> getActiveTasks(UUID playerId) {
        return activeTasks.computeIfAbsent(playerId,
                k -> MMOProfessions.getDataStorage().loadPlayerData(playerId).alchemy_tasks);
    }

    /**
     * 获取玩家解锁的队列数量
     */
    public int getUnlockedQueueCount(UUID playerId) {
        return playerUnlockedQueues.computeIfAbsent(playerId, k -> {
            // 从数据文件加载
            int count = MMOProfessions.getDataStorage().loadPlayerData(playerId).unlocked_queue_count;
            if (count <= 0) {
                // 如果没有数据，使用默认基础队列数
                count = MMOProfessions.getAlchemyManager().getConfigManager().getBaseQueueCount();
            }
            return count;
        });
    }

    /**
     * 检查玩家是否解锁了指定队列
     */
    public boolean isQueueUnlocked(UUID playerId, int queueIndex) {
        return queueIndex < getUnlockedQueueCount(playerId);
    }

    /**
     * 解锁玩家的下一个队列
     */
    public boolean unlockNextQueue(UUID playerId) {
        int currentCount = getUnlockedQueueCount(playerId);
        int maxCount = MMOProfessions.getAlchemyManager().getConfigManager().getMaxQueueCount();

        if (currentCount >= maxCount) {
            return false; // 已达到最大队列数
        }

        playerUnlockedQueues.put(playerId, currentCount + 1);
        MMOProfessions.getDataStorage().savePlayerData(playerId);
        return true;
    }

    /**
     * 设置玩家解锁的队列数量
     */
    public void setUnlockedQueueCount(UUID playerId, int count) {
        int maxCount = MMOProfessions.getAlchemyManager().getConfigManager().getMaxQueueCount();
        count = Math.max(1, Math.min(count, maxCount)); // 限制在1到最大值之间

        playerUnlockedQueues.put(playerId, count);
        MMOProfessions.getDataStorage().savePlayerData(playerId);
    }

    /**
     * 获取玩家的指定队列任务
     */
    public AlchemyTask getPlayerQueueTask(UUID playerId, int queueIndex) {
        return getActiveTasks(playerId).get(queueIndex);
    }

    /**
     * 取消指定玩家的指定队列任务
     */
    public boolean cancelTask(UUID playerId, int queueIndex) {
        Map<Integer, AlchemyTask> playerTasks = activeTasks.get(playerId);
        if (playerTasks != null) {
            AlchemyTask removedTask = playerTasks.remove(queueIndex);
            if (removedTask != null) {
                MMOProfessions.getInstance().getLogger().info(String.format("取消炼金任务: 玩家 %s, 队列 %d",
                        playerId.toString(), queueIndex));
                return true;
            }
        }
        return false;
    }

}
