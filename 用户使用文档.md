# MMOProfessions 用户使用文档

## 📖 插件简介

MMOProfessions 是一个功能丰富的副职业定制插件，支持挖掘、炼金等多种职业系统。插件提供了完整的等级系统、经验获取、区域管理和炼金制作功能。

**支持版本**: Minecraft 1.20+  
**依赖插件**: 无（可选：MMOItems、NeigeItems、PlaceholderAPI）

---

## 🚀 快速开始

### 安装步骤
1. 将插件jar文件放入服务器的 `plugins` 文件夹
2. 重启服务器或使用 `/reload` 命令
3. 插件会自动生成配置文件
4. 根据需要修改配置文件
5. 使用 `/mmop reload` 重新加载配置

### 基础命令
- `/mmop` 或 `/mmoprofessions` 或 `/profession` - 查看插件帮助
- `/mmop reload` - 重新加载所有配置
- `/mmop level [set/view] <玩家> <职业> [等级]` - 等级操作
- `/mmop exp [set/view/add] <玩家> <职业> [数量]` - 经验操作
- `/mmop reset <玩家> [职业]` - 重置玩家职业数据
- `/mmop grant <玩家> <职业>` - 授予玩家职业
- `/mmop revoke <玩家> <职业>` - 取消玩家职业
- `/mmop info` - 查看自身职业信息
- `/mmop area [list/info/reload] [区域ID]` - 区域管理

---

## ⚗️ 炼金系统

### 炼金命令
- `/alchemy` 或 `/alch` - 打开炼金界面
- `/alchemy open` 或 `/alchemy gui` - 打开炼金界面
- `/alchemy status` - 查看当前任务状态（管理员）
- `/alchemy cancel <队列编号>` - 取消指定队列的任务（管理员）
- `/alchemy info <队列编号>` - 查看任务详细信息（管理员）
- `/alchemy unlock <队列编号> [玩家名] ` - 解锁指定编号的队列
- `/alchemy help` - 查看炼金命令帮助

### 炼金界面使用
1. **输入物品**: 将要炼金的物品放入输入槽（蓝色区域）
2. **稳定剂**: 可选择性放入稳定剂提高成功率
3. **确认炼金**: 点击绿色确认按钮开始炼金
4. **队列管理**: 右键点击队列按钮取消任务
5. **获取产物**: 任务完成后从输出槽取出物品

### 炼金配方系统
- 配方支持等级要求，高等级可获得更好的产物
- 支持多种物品来源：原版物品、MMOItems、NeigeItems
- 稳定剂可以提高炼金成功率
- 失败的炼金任务不会产出任何物品

---

## 🏗️ 区域挖掘系统

### 区域特性
- 支持自定义挖掘区域
- 可设置职业等级要求
- 支持工具白名单限制
- 可配置挖掘时间和冷却时间
- 支持自定义掉落物和经验奖励

### 挖掘机制
1. 使用指定工具在配置的区域挖掘
2. 满足职业等级要求才能挖掘
3. 挖掘过程中显示进度条
4. 完成后获得配置的掉落物和经验
5. 区域进入冷却状态

---

## 🎯 职业系统

### 支持的职业
- **mining**: 挖掘职业
- **alchemy**: 炼金职业
- **forging**: 锻造职业
- **lumbering**: 伐木职业

### 等级系统
- 每个职业都有独立的等级和经验系统
- 经验值可通过完成相关活动获得
- 等级提升会解锁新的功能和配方
- 支持最高50级（可配置）

---

## 🎮 PlaceholderAPI 变量

插件支持 PlaceholderAPI，标识符为 `mmop`，可在其他插件中使用以下变量：

### 职业相关变量
- `%mmop_level_<职业ID>%` - 玩家指定职业等级
- `%mmop_exp_<职业ID>%` - 玩家指定职业当前经验
- `%mmop_exp_required_<职业ID>%` - 下一级所需总经验
- `%mmop_exp_to_next_<职业ID>%` - 距离下一级还需经验
- `%mmop_has_<职业ID>%` - 玩家是否拥有指定职业（true/false）

### 炼金系统变量
- `%mmop_alchemy_active_tasks%` - 当前活跃任务数量
- `%mmop_alchemy_completed_tasks%` - 已完成任务数量
- `%mmop_alchemy_queue_<队列编号>_status%` - 指定队列状态（空闲/进行中/已完成）
- `%mmop_alchemy_queue_<队列编号>_remaining_time%` - 指定队列剩余时间（毫秒）
- `%mmop_alchemy_queue_<队列编号>_remaining_time_formatted%` - 指定队列剩余时间（格式化）

### 使用示例
```
职业等级: %mmop_level_mining%
挖掘经验: %mmop_exp_mining%/%mmop_exp_required_mining%
炼金任务: %mmop_alchemy_active_tasks%个进行中
队列1状态: %mmop_alchemy_queue_1_status%
```

---

## 🔑 权限节点

- `mmoprofessions.admin` - 管理员权限（默认：OP）

---

## ❓ 常见问题

### Q: 炼金任务完成后物品消失了？
A: 检查输出槽是否有空位，物品会自动放入输出队列，重新打开界面即可看到。

### Q: 无法挖掘区域？
A: 确认是否满足职业等级要求，是否使用了正确的工具，区域是否在冷却中。

### Q: 配方不生效？
A: 检查配方格式是否正确，物品ID是否存在，等级要求是否满足。

### Q: 如何添加自定义配方？
A: 在 `alchemy.yml` 的 `recipes` 部分添加新配方，参考现有配方格式。

### Q: PlaceholderAPI变量不显示？
A: 确认已安装PlaceholderAPI插件，并且使用正确的变量格式 `%mmop_变量名%`。
