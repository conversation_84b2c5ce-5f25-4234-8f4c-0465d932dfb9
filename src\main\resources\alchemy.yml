# 炼金系统配置文件
# 所有炼金相关的配置都在这个文件中

# 炼金主界面配置
gui:
  title: "§6炼金台"

  # 基础队列数（玩家默认拥有的队列数量）
  base_queue_count: 2

  # 输入槽位置 (玩家可放任意物品)
  input_slots: [12, 13, 20, 21, 22, 23, 29, 30, 31, 32]

  # 输出槽位置 (任务完成后的产物)
  output_slots: [16, 17, 25, 26]

  # 稳定剂槽位 (只能一个)
  stabilizer_slot: 10

  # 队列按钮位置 (最大队列数由此列表长度决定)
  queue_button_slots: [47, 48, 49, 50, 51]

  # 确认按钮位置
  confirm_button_slot: 44

  # 填充槽位
  filler_slots:
    [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      11,
      14,
      15,
      18,
      19,
      24,
      27,
      28,
      33,
      34,
      35,
      36,
      37,
      38,
      39,
      40,
      41,
      42,
      43,
      45,
      46,
      52,
      53,
    ]

  # 格式配置
  formats:
    # 单个物品的显示格式
    # 可用变量: {name} - 物品显示名称, {amount} - 数量
    item_format: "§f  - {name} §7x{amount}"

    # 稳定剂的显示格式
    # 可用变量: {name} - 物品显示名称, {amount} - 数量
    stabilizer_format: "§e  - {name} §7x{amount}"

    # 当没有稳定剂时显示的文本
    no_stabilizer: "§7  - 无"

    # 剩余时间格式化
    remaining_time: "{minutes}min {seconds}s"

  # 按钮配置
  buttons:
    # 确认按钮
    confirm:
      # 物品序列化格式 (https://www.spigotmc.org/wiki/itemstack-serialization/)
      ==: org.bukkit.inventory.ItemStack
      v: 4189
      type: EMERALD
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "§a点击炼金"
        lore:
          - "§7点击开始炼金过程"
          - "§7将消耗输入框中的所有物品"
          - "§7并将任务加入队列"

    # 队列按钮 - 进行时
    queue_active:
      ==: org.bukkit.inventory.ItemStack
      v: 4189
      type: FURNACE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "§a队列 {queue_index} §7(进行中)"
        lore:
          - "§7状态: §a{status}"
          - "§7剩余时间: §e{remaining_time}"
          - "§7投入物品:"
          - "{inputs}"
          - "§7稳定剂:"
          - "{stabilizer}"
          - "§7右键取消任务"

    # 队列按钮 - 待进行
    queue_idle:
      ==: org.bukkit.inventory.ItemStack
      v: 4189
      type: CHEST
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "§7队列 {queue_index} §7(空闲)"
        lore:
          - "§7快开始炼金吧"

    # 队列按钮 - 未解锁
    queue_locked:
      ==: org.bukkit.inventory.ItemStack
      v: 4189
      type: BARRIER
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: "§c队列 {queue_index} §7(未解锁)"
        lore:
          - "§7此队列尚未解锁"

    # 填充物品
    filler:
      ==: org.bukkit.inventory.ItemStack
      v: 4189
      type: GRAY_STAINED_GLASS_PANE
      meta:
        ==: ItemMeta
        meta-type: UNSPECIFIC
        display-name: " "

  # 音效配置
  sounds:
    # GUI相关音效
    gui_open: "UI_BUTTON_CLICK" # GUI打开音效
    gui_close: "UI_TOAST_OUT" # GUI关闭音效
    button_click: "UI_BUTTON_CLICK" # 按钮点击音效
    item_place: "BLOCK_STONE_PLACE" # 放置物品音效
    item_take: "ENTITY_ITEM_PICKUP" # 取出物品音效

    # 任务相关音效
    task_start: "BLOCK_NOTE_BLOCK_PLING" # 开始任务音效
    task_cancel: "BLOCK_NOTE_BLOCK_BASS" # 取消任务音效

    # 错误提示音效
    error: "ENTITY_VILLAGER_NO" # 错误音效

    # 自定义音效示例（支持命名空间格式）
    # "myplugin:custom_sound"

# 炼金配方配置
recipes:
  # 示例配方 - 铁锭炼金
  - inputs:
      - source: vanilla
        value: IRON_INGOT
        amount: 2
      - source: vanilla
        value: COAL
        amount: 1
    level_choices:
      # 最少需要 10 级才能产出
      10:
        # 输出物品
        outputs:
          - source: vanilla
            value: DIAMOND
            amount: 30
        # 炼金时间 (秒)
        duration: 30
        # 基础成功率 (0.0 - 1.0)
        base_success_rate: 0.4
      # 当以上等级都不满足时，使用此配置, 若此时配方没有配置 default ，则直接失败
      default:
        outputs:
          - source: vanilla
            value: IRON_INGOT
            amount: 20
        duration: 30
        base_success_rate: 0.4

  # 示例配方 - 钻石炼金
  - inputs:
      - source: vanilla
        value: DIAMOND
        amount: 1
      - source: vanilla
        value: COAL
        amount: 1
    level_choices:
      20:
        outputs:
          - source: vanilla
            value: DIAMOND
            amount: 64
          - source: vanilla
            value: COAL
            amount: 64
        duration: 10
        base_success_rate: 1
      default:
        outputs:
          - source: vanilla
            value: IRON_INGOT
            amount: 64
        duration: 30
        base_success_rate: 0.4
# 稳定剂效果配置
stabilizers:
  - ingredient:
      source: mmoitems
      value: mmoitem{type=MATERIAL, id=STEEL_INGOT}
    # 添加炼药成功率
    bonus: 0.1
  - ingredient:
      source: vanilla
      value: REDSTONE
    # 添加炼药成功率
    bonus: 0.05
  - ingredient:
      source: neigeitems
      value: ExampleItem
    # 添加炼药成功率
    bonus: 0.8
