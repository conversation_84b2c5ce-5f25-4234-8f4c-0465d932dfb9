<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>cn.fd.customize</groupId>
  <artifactId>MMOProfessions</artifactId>
  <version>1.2.2</version>
  <packaging>jar</packaging>

  <name>MMOProfessions</name>

  <properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <relocations>
                <relocation>
                  <pattern>com.google.gson</pattern>
                  <shadedPattern>cn.fd.customize.mmoProfessions.libs.gson</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
  </build>

  <repositories>
      <repository>
          <id>spigotmc-repo</id>
          <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
      </repository>
      <repository>
          <id>sonatype</id>
          <url>https://oss.sonatype.org/content/groups/public/</url>
      </repository>
      <repository>
          <id>phoenix</id>
          <url>https://nexus.phoenixdevt.fr/repository/maven-public/</url>
      </repository>
      <repository>
          <id>placeholderapi</id>
          <url>https://repo.extendedclip.com/content/repositories/placeholderapi/</url>
      </repository>
  </repositories>

  <dependencies>
      <dependency>
          <groupId>org.spigotmc</groupId>
          <artifactId>spigot-api</artifactId>
          <version>1.21-R0.1-SNAPSHOT</version>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>io.lumine</groupId>
          <artifactId>MythicLib-dist</artifactId>
          <version>1.7.1-SNAPSHOT</version>
          <scope>provided</scope>
          <optional>true</optional>
      </dependency>
      <dependency>
          <groupId>net.Indyuce</groupId>
          <artifactId>MMOItems-API</artifactId>
          <version>6.10.1-SNAPSHOT</version>
          <scope>provided</scope>
      </dependency>
      <!-- Gson for JSON processing -->
      <dependency>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
          <version>2.10.1</version>
      </dependency>
      <!-- PlaceholderAPI -->
      <dependency>
          <groupId>me.clip</groupId>
          <artifactId>placeholderapi</artifactId>
          <version>2.11.6</version>
          <scope>provided</scope>
          <optional>true</optional>
      </dependency>
      <!-- NeigeItems (本地依赖) -->
      <dependency>
          <groupId>pers.neige</groupId>
          <artifactId>neigeitems</artifactId>
          <version>1.21.94</version>
          <scope>system</scope>
          <systemPath>${project.basedir}/libs/NeigeItems-1.21.94-api.jar</systemPath>
          <optional>true</optional>
      </dependency>
  </dependencies>
</project>
