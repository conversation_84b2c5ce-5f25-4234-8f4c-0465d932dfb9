package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.config.AlchemyConfigLoader;
import cn.fd.customize.mmoProfessions.professions.AlchemyProfession;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 炼金主界面GUI
 * 包含输入框、确认按钮、队列按钮、输出框、稳定剂槽等
 */
public class AlchemyMainGUI {

    private final AlchemyManager alchemyManager;
    private final Map<UUID, Inventory> playerInventories;
    private final Map<UUID, List<ItemStack>> playerOutputQueue; // 玩家输出物品队列

    // 配置项
    private String title;

    private List<Integer> inputSlots;
    private List<Integer> outputSlots;
    private List<Integer> queueButtonSlots;
    private int stabilizerSlot;
    private int confirmButtonSlot;
    private List<Integer> fillerSlots;
    private ItemStack confirmButton;
    private ItemStack queueActiveButton;
    private ItemStack queueIdleButton;
    private ItemStack queueLockedButton;
    private ItemStack fillerItem;

    // 队列按钮刷新任务
    private static BukkitTask queueRefreshTask;

    public AlchemyMainGUI(AlchemyManager alchemyManager) {
        this.alchemyManager = alchemyManager;
        this.playerInventories = new HashMap<>();
        this.playerOutputQueue = new HashMap<>();

        loadConfiguration();
    }

    /**
     * 启动队列按钮刷新定时器
     */
    public void startQueueRefreshTask() {
        // 启动新的刷新任务
        queueRefreshTask = Bukkit.getScheduler().runTaskTimer(MMOProfessions.getInstance(), () -> {
            // 遍历所有打开炼金界面的在线玩家
            for (Map.Entry<UUID, Inventory> entry : playerInventories.entrySet()) {
                UUID playerId = entry.getKey();
                Inventory inventory = entry.getValue();

                Player player = Bukkit.getPlayer(playerId);
                if (player != null && player.isOnline()) {
                    // 检查玩家是否还在查看炼金界面
                    if (isAlchemyMainGUI(player, player.getOpenInventory().getTopInventory())) {
                        checkCompletedTasks(playerId);
                        // 更新输出槽显示（如果有新的输出物品）
                        updateOutputSlots(inventory, player);
                        // 更新队列按钮
                        updateQueueButtons(inventory, player);
                    }
                }
            }
        }, 20L, 20L); // 每秒刷新一次
    }

    public void cancelQueueRefreshTimer() {
        if (queueRefreshTask != null) {
            queueRefreshTask.cancel();
        }
    }

    /**
     * 检查所有玩家的已完成任务
     */
    private void checkCompletedTasks(UUID playerUuid) {
        Map<Integer, AlchemyTask> activeTasks = alchemyManager.getTaskManager().getActiveTasks(playerUuid);
        // 使用迭代器安全地删除已完成的任务
        Iterator<Map.Entry<Integer, AlchemyTask>> taskIterator = activeTasks.entrySet().iterator();
        while (taskIterator.hasNext()) {
            Map.Entry<Integer, AlchemyTask> taskEntry = taskIterator.next();
            AlchemyTask task = taskEntry.getValue();

            if (task.isCompleted()) {
                // 任务完成，将输出物品添加到队列
                if (!task.getOutputItems().isEmpty()) {
                    addOutputItems(playerUuid, task.getOutputItems());
                }

                // 删除已完成的任务
                taskIterator.remove();

                // 保存数据
                MMOProfessions.getDataStorage().savePlayerData(playerUuid);
            }
        }
    }

    /**
     * 加载配置
     */
    private void loadConfiguration() {
        AlchemyConfigLoader configManager = alchemyManager.getConfigManager();

        // 直接从配置文件获取配置项
        title = configManager.getConfig().getString("gui.title");
        inputSlots = configManager.getConfig().getIntegerList("gui.input_slots");
        outputSlots = configManager.getConfig().getIntegerList("gui.output_slots");
        stabilizerSlot = configManager.getConfig().getInt("gui.stabilizer_slot");
        queueButtonSlots = configManager.getQueueButtonSlots();
        confirmButtonSlot = configManager.getConfig().getInt("gui.confirm_button_slot");
        fillerSlots = configManager.getConfig().getIntegerList("gui.filler_slots");

        // 加载按钮物品
        confirmButton = configManager.createItemFromConfig("gui.buttons.confirm");
        queueActiveButton = configManager.createItemFromConfig("gui.buttons.queue_active");
        queueIdleButton = configManager.createItemFromConfig("gui.buttons.queue_idle");
        queueLockedButton = configManager.createItemFromConfig("gui.buttons.queue_locked");
        fillerItem = configManager.createItemFromConfig("gui.buttons.filler");
    }

    /**
     * 打开炼金界面
     */
    public void openGUI(Player player) {
        Inventory inventory = Bukkit.createInventory(null, 54, title);
        checkCompletedTasks(player.getUniqueId());

        // 设置填充物品
        for (int slot : fillerSlots) {
            if (slot < 0 || slot > 53
                    || outputSlots.contains(slot)
                    || inputSlots.contains(slot)
                    || stabilizerSlot == slot) {
                continue;
            }
            inventory.setItem(slot, fillerItem.clone());
        }

        // 设置确认按钮
        inventory.setItem(confirmButtonSlot, confirmButton.clone());

        // 设置队列按钮
        updateQueueButtons(inventory, player);

        // 恢复玩家之前的物品（如果有）
        restorePlayerItems(inventory, player);

        playerInventories.put(player.getUniqueId(), inventory);
        player.openInventory(inventory);

        // 播放GUI打开音效
        playSound(player, "gui_open");
    }

    /**
     * 刷新输出槽显示
     * 清空所有输出槽并重新显示队列中的物品
     */
    private void updateOutputSlots(Inventory inventory, Player player) {
        // 清空所有输出槽
        for (int slot : outputSlots) {
            inventory.setItem(slot, null);
        }

        // 重新显示队列中的物品
        List<ItemStack> outputQueue = playerOutputQueue.get(player.getUniqueId());
        if (outputQueue != null && !outputQueue.isEmpty()) {
            int slotIndex = 0;
            for (int i = 0; i < outputQueue.size() && slotIndex < outputSlots.size(); i++) {
                ItemStack output = outputQueue.get(i);
                if (output != null && !output.getType().isAir()) {
                    inventory.setItem(outputSlots.get(slotIndex), output.clone());
                    slotIndex++;
                }
            }
        }
    }

    /**
     * 更新队列按钮
     */
    private void updateQueueButtons(Inventory inventory, Player player) {
        for (int queueIndex = 0; queueIndex < queueButtonSlots.size(); queueIndex++) {
            int slot = queueButtonSlots.get(queueIndex);

            // 获取队列任务
            AlchemyTask task = alchemyManager.getTaskManager().getPlayerQueueTask(player.getUniqueId(), queueIndex);

            ItemStack button;

            // 检查队列是否解锁
            if (!alchemyManager.getTaskManager().isQueueUnlocked(player.getUniqueId(), queueIndex)) {
                // 未解锁的队列
                button = queueLockedButton.clone();
            } else {

                if (task != null && !task.isCompleted()) {
                    // 进行中的任务
                    button = queueActiveButton.clone();
                } else {
                    // 空闲队列
                    button = queueIdleButton.clone();
                }
            }

            // 处理按钮占位符
            alchemyManager.getConfigManager().processButtonMeta(button, task, queueIndex);

            inventory.setItem(slot, button);
        }
    }

    /**
     * 恢复玩家物品
     */
    private void restorePlayerItems(Inventory inventory, Player player) {
        // 检查是否有等待显示的输出物品队列
        List<ItemStack> outputQueue = playerOutputQueue.get(player.getUniqueId());
        if (outputQueue != null && !outputQueue.isEmpty()) {
            // 显示输出物品到输出槽位，但不从队列中移除
            int slotIndex = 0;
            for (int i = 0; i < outputQueue.size() && slotIndex < outputSlots.size(); i++) {
                ItemStack output = outputQueue.get(i);
                if (output != null && !output.getType().isAir()) {
                    inventory.setItem(outputSlots.get(slotIndex), output.clone());
                    slotIndex++;
                }
            }
        }
    }

    /**
     * 检查是否是玩家的炼金界面
     */
    public boolean isAlchemyMainGUI(Player player, Inventory inventory) {
        Inventory playerInv = playerInventories.get(player.getUniqueId());
        return playerInv != null && playerInv.equals(inventory);
    }

    /**
     * 处理界面点击事件
     */
    public void handleInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        Inventory inventory = playerInventories.get(player.getUniqueId());

        if (inventory == null || !event.getInventory().equals(inventory)) {
            return;
        }

        int slot = event.getRawSlot();

        // 检查是否不在此界面内点击
        if (slot < 0 || slot > 53) {
            return;
        }

        // 允许在输入槽位和稳定剂槽位进行操作
        if (inputSlots.contains(slot) || stabilizerSlot == slot) {
            return;
        }

        // 检查是否点击输出槽
        if (outputSlots.contains(slot)) {
            handleOutputClick(player, inventory, slot, event);
            return;
        }

        // 检查是否点击了确认按钮
        if (slot == confirmButtonSlot) {
            event.setCancelled(true);
            handleConfirmClick(player, inventory);
            return;
        }

        // 检查是否点击了队列按钮
        if (queueButtonSlots.contains(slot)) {
            event.setCancelled(true);
            int queueIndex = queueButtonSlots.indexOf(slot);
            handleQueueClick(player, queueIndex, event);
            return;
        }

        // 所有其他槽位（包括填充物品槽位和未设置物品的空位）都禁止操作
        event.setCancelled(true);
    }

    /**
     * 处理确认按钮点击
     */
    private void handleConfirmClick(Player player, Inventory inventory) {
        // 播放按钮点击音效
        playSound(player, "button_click");

        // 收集输入物品
        List<ItemStack> inputItems = new ArrayList<>();
        for (int slot : inputSlots) {
            ItemStack item = inventory.getItem(slot);
            if (item != null && !item.getType().isAir()) {
                inputItems.add(item.clone());
            }
        }

        if (inputItems.isEmpty()) {
            // 播放错误音效
            playSound(player, "error");
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.no_input_items");
            return;
        }

        // 收集稳定剂
        ItemStack stabilizerItem = inventory.getItem(stabilizerSlot);
        if (stabilizerItem != null && stabilizerItem.getType().isAir()) {
            stabilizerItem = null;
        }

        // 匹配配方
        AlchemyRecipe recipe = alchemyManager.getRecipeManager().matchRecipe(inputItems);

        List<ItemStack> outputItems;
        long duration;

        if (recipe == null) {
            // 未找到配方，创建失败任务（无输出物品，使用默认时间）
            outputItems = new ArrayList<>(); // 空输出物品列表表示失败
            duration = 30 * 1000; // 默认30秒时间
        } else {
            // 获取适配的等级选择
            LevelChoice choice = recipe.getAdaptedChoice(AlchemyProfession.getInstance().getLevel(player));
            if (choice == null) {
                // 等级不足，检查default选择
                LevelChoice defaultChoice = recipe.getDefaultChoice();
                if (defaultChoice == null) {
                    // 没有default选择，任务失败，无输出物品
                    outputItems = new ArrayList<>();
                    duration = 30 * 1000; // 默认30秒时间
                } else {
                    // 使用default选择
                    outputItems = defaultChoice.calculateOutput(stabilizerItem, player);
                    duration = defaultChoice.getDuration() * 1000;
                }
            } else {
                // 计算输出物品和时间
                outputItems = choice.calculateOutput(stabilizerItem, player);
                duration = choice.getDuration() * 1000; // 转换为毫秒
            }

            // 任务失败，输出失败物品
            if (outputItems.isEmpty()) {
                outputItems = recipe.getFailOutputs().stream().map(output -> output.generateOutput(player))
                        .collect(Collectors.toList());
            }
        }

        // 选择队列（选择任务最少的队列）
        int selectedQueue = selectBestQueue(player);

        // 创建任务
        if (alchemyManager.getTaskManager().isQueueUnlocked(player.getUniqueId(), selectedQueue)) {
            AlchemyTask task = alchemyManager.getTaskManager().createTask(player, selectedQueue, inputItems,
                    stabilizerItem,
                    outputItems, duration);

            // 清空输入槽和稳定剂槽
            for (int slot : inputSlots) {
                inventory.setItem(slot, null);
            }
            inventory.setItem(stabilizerSlot, null);

            // 更新队列按钮
            updateQueueButtons(inventory, player);

            // 播放任务开始音效
            playSound(player, "task_start");

            String formattedTime = alchemyManager.getConfigManager().formatRemainingTime(task.getDuration());
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.task_created",
                    String.valueOf(selectedQueue), formattedTime);
        } else {
            // 播放错误音效
            playSound(player, "queue_locked_click");
            MMOProfessions.getMessageManager().sendMessage(player, "alchemy.queue_locked",
                    String.valueOf(selectedQueue));
        }
    }

    /**
     * 处理队列按钮点击
     */
    private void handleQueueClick(Player player, int queueIndex, InventoryClickEvent event) {
        // 检查队列是否解锁
        if (!alchemyManager.getTaskManager().isQueueUnlocked(player.getUniqueId(), queueIndex)) {
            return; // 未解锁的队列不响应任何点击
        }

        // 只处理右键点击取消队列
        if (event.isRightClick()) {
            AlchemyTask task = alchemyManager.getTaskManager().getPlayerQueueTask(player.getUniqueId(), queueIndex);

            if (task != null && !task.isCompleted()) {
                // 取消任务
                if (alchemyManager.getTaskManager().cancelTask(player.getUniqueId(), queueIndex)) {
                    // 播放任务取消音效
                    playSound(player, "task_cancel");

                    // 更新队列按钮显示
                    updateQueueButtons(playerInventories.get(player.getUniqueId()), player);
                    MMOProfessions.getMessageManager().sendMessage(player, "alchemy.task_cancelled",
                            String.valueOf(queueIndex));
                }
            }
        }
        // 其他所有点击动作都被忽略
    }

    /**
     * 处理输出槽点击
     */
    private void handleOutputClick(Player player, Inventory inventory, int slot, InventoryClickEvent event) {
        // 检查是否是Shift+点击操作
        if (event.getAction() == InventoryAction.MOVE_TO_OTHER_INVENTORY) {
            // 对于Shift+点击，只允许从output槽拿出物品到背包
            // 检查点击的槽位是否有物品，如果有物品则允许（拿出），如果没有物品则禁止（防止放入）
            ItemStack clickedItem = inventory.getItem(slot);
            if (clickedItem == null || clickedItem.getType().isAir()) {
                // 槽位为空，这意味着玩家试图向output槽放入物品，禁止此操作
                event.setCancelled(true);
                return;
            }
            // 槽位有物品，允许拿出操作
        } else {
            // 对于其他操作，只允许拿出物品的操作
            switch (event.getAction()) {
                case PICKUP_ONE:
                case PICKUP_ALL:
                case PICKUP_HALF:
                case PICKUP_SOME:
                    // 允许这些拿出物品的操作
                    break;
                default:
                    // 禁止其他所有操作（包括拖拽放入、交换等）
                    event.setCancelled(true);
                    return;
            }
        }

        // 记录点击前的物品状态，用于后续比较
        ItemStack beforeItem = inventory.getItem(slot);
        ItemStack beforeItemClone = beforeItem != null ? beforeItem.clone() : null;

        // 延迟检查物品变化，确保物品取出操作已完成
        Bukkit.getScheduler().runTaskLater(MMOProfessions.getInstance(), () -> {
            // 检查玩家是否还在线且界面仍然打开
            if (!player.isOnline() || !isAlchemyMainGUI(player, player.getOpenInventory().getTopInventory())) {
                return;
            }

            // 检查槽位是否已经被清空或物品数量减少
            ItemStack currentItem = inventory.getItem(slot);
            List<ItemStack> outputQueue = playerOutputQueue.get(player.getUniqueId());

            if (outputQueue != null && !outputQueue.isEmpty()) {
                // 计算当前输出槽中的物品索引
                int slotIndex = outputSlots.indexOf(slot);
                if (slotIndex >= 0 && slotIndex < outputQueue.size()) {
                    ItemStack queueItem = outputQueue.get(slotIndex);

                    // 检查物品是否发生了变化
                    boolean itemChanged = false;

                    if (beforeItemClone == null || beforeItemClone.getType().isAir()) {
                        // 之前没有物品，现在也不应该有变化
                        return;
                    }

                    if (currentItem == null || currentItem.getType().isAir()) {
                        // 槽位被完全清空，从队列中移除这个物品
                        outputQueue.remove(slotIndex);
                        itemChanged = true;
                    } else if (queueItem != null && currentItem.getType() == queueItem.getType()) {
                        // 检查数量是否减少
                        if (currentItem.getAmount() < beforeItemClone.getAmount()) {
                            // 物品数量减少，更新队列中的数量
                            queueItem.setAmount(currentItem.getAmount());
                            if (queueItem.getAmount() <= 0) {
                                outputQueue.remove(slotIndex);
                            }
                            itemChanged = true;
                        }
                    }

                    // 只有在物品确实发生变化时才更新显示和保存数据
                    if (itemChanged) {
                        // 播放取出物品音效
                        playSound(player, "item_take");

                        // 重新排列输出槽显示
                        updateOutputSlots(inventory, player);

                        // 如果队列为空，移除玩家的输出队列
                        if (outputQueue.isEmpty()) {
                            playerOutputQueue.remove(player.getUniqueId());
                        }

                        // 立即保存玩家数据
                        MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());
                    }
                }
            }
        }, 1L); // 延迟1tick执行
    }

    /**
     * 处理界面关闭事件
     */
    public void handleInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();
        Inventory inventory = playerInventories.remove(player.getUniqueId());

        if (inventory != null) {
            // 播放GUI关闭音效
            playSound(player, "gui_close");

            // 返还输入槽和稳定剂槽中的物品
            returnItems(player, inventory);
        }
    }

    /**
     * 处理拖拽事件
     */
    public void handleInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        Inventory inventory = playerInventories.get(player.getUniqueId());

        if (inventory == null || !event.getInventory().equals(inventory)) {
            return;
        }

        // 检查拖拽的槽位，只允许在输入槽位和稳定剂槽位进行拖拽
        for (int slot : event.getRawSlots()) {
            // 如果拖拽涉及界面内的槽位，但不是输入槽位或稳定剂槽位，则取消事件
            if (slot >= 0 && slot <= 53) {
                if (!inputSlots.contains(slot) && slot != stabilizerSlot) {
                    event.setCancelled(true);
                    return;
                }
            }
        }
    }

    /**
     * 添加输出物品到玩家队列
     */
    public void addOutputItems(UUID playerId, List<ItemStack> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        List<ItemStack> queue = playerOutputQueue.computeIfAbsent(playerId, k -> new ArrayList<>());
        queue.addAll(items);
    }

    /**
     * 获取玩家的输出队列
     */
    public List<ItemStack> getPlayerOutputQueue(UUID playerId) {
        return playerOutputQueue.getOrDefault(playerId, new ArrayList<>());
    }

    /**
     * 设置玩家的输出队列
     */
    public void setPlayerOutputQueue(UUID playerId, List<ItemStack> queue) {
        if (queue == null || queue.isEmpty()) {
            playerOutputQueue.remove(playerId);
        } else {
            playerOutputQueue.put(playerId, new ArrayList<>(queue));
        }
    }

    /**
     * 返还物品给玩家
     */
    private void returnItems(Player player, Inventory inventory) {
        List<ItemStack> itemsToReturn = new ArrayList<>();

        // 收集输入槽物品
        for (int slot : inputSlots) {
            ItemStack item = inventory.getItem(slot);
            if (item != null && !item.getType().isAir()) {
                itemsToReturn.add(item);
            }
        }

        // 收集稳定剂槽物品
        ItemStack stabilizerItem = inventory.getItem(stabilizerSlot);
        if (stabilizerItem != null && !stabilizerItem.getType().isAir()) {
            itemsToReturn.add(stabilizerItem);
        }

        // 返还物品
        for (ItemStack item : itemsToReturn) {
            if (player.getInventory().firstEmpty() != -1) {
                player.getInventory().addItem(item);
            } else {
                player.getWorld().dropItemNaturally(player.getLocation(), item);
            }
        }
    }

    /**
     * 选择最佳队列
     */
    private int selectBestQueue(Player player) {
        int bestQueue = 0;
        int minTasks = Integer.MAX_VALUE;

        for (int i = 0; i < queueButtonSlots.size(); i++) {
            AlchemyTask task = alchemyManager.getTaskManager().getPlayerQueueTask(player.getUniqueId(), i);
            int taskCount = (task != null && !task.isCompleted()) ? 1 : 0;
            if (taskCount < minTasks) {
                minTasks = taskCount;
                bestQueue = i;
            }
        }

        return bestQueue;
    }

    /**
     * 重新加载GUI配置
     */
    public void reloadConfiguration() {
        // 重新加载配置
        loadConfiguration();

        // 更新所有打开的界面
        for (Map.Entry<UUID, Inventory> entry : playerInventories.entrySet()) {
            UUID playerId = entry.getKey();
            Player player = Bukkit.getPlayer(playerId);

            if (player != null && player.isOnline()) {
                // 关闭当前界面并重新打开
                player.closeInventory();
                openGUI(player);
            }
        }
    }

    /**
     * 播放音效的辅助方法
     *
     * @param player   玩家
     * @param soundKey 音效键名
     */
    private void playSound(Player player, String soundKey) {
        String soundConfig = alchemyManager.getConfigManager().getSound(soundKey);
        if (soundConfig != null && !soundConfig.trim().isEmpty()) {
            player.playSound(player.getLocation(), soundConfig, 1f, 1f);
        }
    }

}
