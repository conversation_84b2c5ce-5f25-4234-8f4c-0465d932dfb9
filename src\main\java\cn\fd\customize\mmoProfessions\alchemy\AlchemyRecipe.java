package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.compat.ItemSource;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;

/**
 * 炼金配方数据类
 * 表示一个完整的炼金配方，包含输入物品、等级选择和默认选择
 */
public class AlchemyRecipe {
    private final int id;
    private final List<ItemSource> inputs; // 输入物品列表
    private final List<ItemSource> failOutputs; // 失败产物列表
    private final List<LevelChoice> levelChoices; // 等级选择
    private final LevelChoice defaultChoice; // 默认选择

    /**
     * 构造函数
     *
     * @param id            配方ID
     * @param inputs        输入物品成分列表
     * @param levelChoices  等级选择列表
     * @param defaultChoice 默认选择
     */
    public AlchemyRecipe(int id, List<ItemSource> inputs, List<LevelChoice> levelChoices,
            LevelChoice defaultChoice) {
        this(id, inputs, new ArrayList<>(), levelChoices, defaultChoice);
    }

    /**
     * 构造函数（包含失败产物）
     *
     * @param id            配方ID
     * @param inputs        输入物品成分列表
     * @param failOutputs   失败产物列表
     * @param levelChoices  等级选择列表
     * @param defaultChoice 默认选择
     */
    public AlchemyRecipe(int id, List<ItemSource> inputs, List<ItemSource> failOutputs,
            List<LevelChoice> levelChoices, LevelChoice defaultChoice) {
        this.id = id;
        this.inputs = new ArrayList<>(inputs);
        this.failOutputs = new ArrayList<>(failOutputs != null ? failOutputs : new ArrayList<>());
        this.levelChoices = new ArrayList<>(levelChoices);
        this.defaultChoice = defaultChoice;
    }

    /**
     * 获取配方ID
     * 
     * @return 配方ID
     */
    public int getId() {
        return id;
    }

    /**
     * 获取输入物品成分列表
     * 
     * @return 输入物品成分列表的副本
     */
    public List<ItemSource> getInputs() {
        return new ArrayList<>(inputs);
    }

    /**
     * 获取等级选择列表
     * 
     * @return 等级选择列表的副本
     */
    public List<LevelChoice> getLevelChoices() {
        return new ArrayList<>(levelChoices);
    }

    /**
     * 获取默认选择
     * 
     * @return 默认选择
     */
    public LevelChoice getDefaultChoice() {
        return defaultChoice;
    }

    /**
     * 根据玩家等级获取适配选择
     * 
     * @param playerLevel 玩家等级
     * @return 适配的等级选择，如果没有合适的则返回默认选择
     */
    public LevelChoice getAdaptedChoice(int playerLevel) {
        LevelChoice bestChoice = null;
        int bestLevel = -1;

        // 遍历等级选择，找到玩家等级满足的最高等级要求
        for (LevelChoice choice : levelChoices) {
            int requiredLevel = choice.getRequiredLevel();
            if (playerLevel >= requiredLevel && requiredLevel > bestLevel) {
                bestChoice = choice;
                bestLevel = requiredLevel;
            }
        }

        // 如果没有找到合适的等级选择，使用默认选择
        if (bestChoice == null) {
            bestChoice = defaultChoice;
        }

        return bestChoice;
    }

    /**
     * 检查提供的物品是否全部匹配配方要求
     * 
     * @param providedItems 提供的物品列表
     * @return 是否匹配配方要求
     */
    public boolean matches(List<ItemStack> providedItems) {
        // 检查每个提供的物品是否都能匹配至少一个输入要求
        for (ItemStack provided : providedItems) {
            boolean singleMatch = false;
            for (ItemSource ingredient : inputs) {
                if (ingredient.matchesIngredient(provided)) {
                    singleMatch = true;
                    break;
                }
            }
            if (!singleMatch) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取失败产物列表
     *
     * @return 失败产物列表的副本
     */
    public List<ItemSource> getFailOutputs() {
        return new ArrayList<>(failOutputs);
    }

}
