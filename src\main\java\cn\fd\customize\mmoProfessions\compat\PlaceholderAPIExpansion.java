package cn.fd.customize.mmoProfessions.compat;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.alchemy.AlchemyTask;
import cn.fd.customize.mmoProfessions.core.ProfessionData;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.bukkit.entity.Player;

import java.util.Map;

/**
 * PlaceholderAPI 扩展
 * 提供 MMOProfessions 相关的占位符变量
 */
public class PlaceholderAPIExpansion extends PlaceholderExpansion {

    @Override
    public String getIdentifier() {
        return "mmop";
    }

    @Override
    public String getAuthor() {
        return "蛟龙";
    }

    @Override
    public String getVersion() {
        return MMOProfessions.getInstance().getDescription().getVersion();
    }

    @Override
    public boolean persist() {
        return true; // 插件重载时保持注册
    }

    @Override
    public String onPlaceholderRequest(Player player, String params) {
        if (player == null) {
            return "";
        }

        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(player);
        
        // 职业等级相关占位符
        // %mmoprofessions_level_<职业ID>%
        if (params.startsWith("level_")) {
            String professionId = params.substring(6);
            return String.valueOf(data.getLevel(professionId));
        }
        
        // 职业经验相关占位符
        // %mmoprofessions_exp_<职业ID>%
        if (params.startsWith("exp_")) {
            String professionId = params.substring(4);
            return String.valueOf(data.getExperience(professionId));
        }
        
        // 职业升级所需经验
        // %mmoprofessions_exp_required_<职业ID>%
        if (params.startsWith("exp_required_")) {
            String professionId = params.substring(13);
            int currentLevel = data.getLevel(professionId);
            return String.valueOf(MMOProfessions.getExperienceManager().getRequiredExp(professionId, currentLevel + 1));
        }
        
        // 距离下一级所需经验
        // %mmoprofessions_exp_to_next_<职业ID>%
        if (params.startsWith("exp_to_next_")) {
            String professionId = params.substring(12);
            int currentExp = data.getExperience(professionId);
            return String.valueOf(MMOProfessions.getExperienceManager().getExpToNextLevel(professionId, currentExp));
        }
        
        // 炼金系统相关占位符
        // %mmoprofessions_alchemy_active_tasks%
        if (params.equals("alchemy_active_tasks")) {
            Map<Integer, AlchemyTask> tasks = MMOProfessions.getAlchemyManager().getTaskManager().getActiveTasks(player.getUniqueId());
            return String.valueOf(tasks.size());
        }
        
        // %mmoprofessions_alchemy_completed_tasks%
        if (params.equals("alchemy_completed_tasks")) {
            Map<Integer, AlchemyTask> tasks = MMOProfessions.getAlchemyManager().getTaskManager().getActiveTasks(player.getUniqueId());
            long completedCount = tasks.values().stream().filter(AlchemyTask::isCompleted).count();
            return String.valueOf(completedCount);
        }
        
        // %mmoprofessions_alchemy_queue_<队列编号>_status%
        if (params.startsWith("alchemy_queue_") && params.endsWith("_status")) {
            try {
                String queueStr = params.substring(14, params.length() - 7);
                int queueIndex = Integer.parseInt(queueStr);
                AlchemyTask task = MMOProfessions.getAlchemyManager().getTaskManager().getPlayerQueueTask(player.getUniqueId(), queueIndex);
                if (task == null) {
                    return "空闲";
                } else if (task.isCompleted()) {
                    return "已完成";
                } else {
                    return "进行中";
                }
            } catch (NumberFormatException e) {
                return "";
            }
        }
        
        // %mmoprofessions_alchemy_queue_<队列编号>_remaining_time%
        if (params.startsWith("alchemy_queue_") && params.endsWith("_remaining_time")) {
            try {
                String queueStr = params.substring(14, params.length() - 15);
                int queueIndex = Integer.parseInt(queueStr);
                AlchemyTask task = MMOProfessions.getAlchemyManager().getTaskManager().getPlayerQueueTask(player.getUniqueId(), queueIndex);
                if (task == null || task.isCompleted()) {
                    return "0";
                } else {
                    return String.valueOf(task.getRemainingTime());
                }
            } catch (NumberFormatException e) {
                return "";
            }
        }
        
        // %mmoprofessions_alchemy_queue_<队列编号>_remaining_time_formatted%
        if (params.startsWith("alchemy_queue_") && params.endsWith("_remaining_time_formatted")) {
            try {
                String queueStr = params.substring(14, params.length() - 25);
                int queueIndex = Integer.parseInt(queueStr);
                AlchemyTask task = MMOProfessions.getAlchemyManager().getTaskManager().getPlayerQueueTask(player.getUniqueId(), queueIndex);
                if (task == null || task.isCompleted()) {
                    return "00:00";
                } else {
                    return MMOProfessions.getAlchemyManager().getConfigManager().formatRemainingTime(task.getRemainingTime());
                }
            } catch (NumberFormatException e) {
                return "";
            }
        }
        
        // 检查玩家是否拥有某个职业
        // %mmoprofessions_has_<职业ID>%
        if (params.startsWith("has_")) {
            String professionId = params.substring(4);
            return data.hasProfession(professionId) ? "true" : "false";
        }

        return null; // 未知占位符
    }
}
