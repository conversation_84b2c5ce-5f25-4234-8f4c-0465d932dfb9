package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.config.AlchemyConfigLoader;

/**
 * 炼金系统管理器
 * 统一管理炼金任务、UI界面
 */
public class AlchemyManager {

    private final AlchemyConfigLoader configManager;
    private final AlchemyTaskManager taskManager;
    private final AlchemyMainGUI mainGUI;
    private final AlchemyRecipeManager recipeManager;

    public AlchemyManager() {
        this.configManager = new AlchemyConfigLoader();
        this.taskManager = new AlchemyTaskManager();
        this.recipeManager = new AlchemyRecipeManager(this);
        this.mainGUI = new AlchemyMainGUI(this);

        MMOProfessions.getInstance().getLogger().info("炼金系统已初始化");
    }

    public AlchemyMainGUI getMainGUI() {
        return mainGUI;
    }



    public AlchemyTaskManager getTaskManager() {
        return taskManager;
    }

    public AlchemyRecipeManager getRecipeManager() {
        return recipeManager;
    }

    public AlchemyConfigLoader getConfigManager() {
        return configManager;
    }

    /**
     * 重新加载炼金系统配置
     */
    public void reload() {
        MMOProfessions.getInstance().getLogger().info("正在重新加载炼金系统配置...");

        // 重新加载配置文件
        configManager.reloadConfig();

        // 重新初始化配方管理器
        recipeManager.reloadRecipes();

        // 重新加载GUI配置
        mainGUI.reloadConfiguration();

        MMOProfessions.getInstance().getLogger().info("炼金系统配置重新加载完成");
    }

}
